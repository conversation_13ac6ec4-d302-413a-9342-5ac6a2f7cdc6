import { Metadata } from "next";
import { Mdx } from "@/components/mdx/mdx-components";

export const metadata: Metadata = {
  title: "Authentication | Instabee API Documentation",
  description: "How to authenticate with the Instabee API",
}

import fs from 'fs';
import path from 'path';

function getAuthContent() {
  const filePath = path.join(process.cwd(), 'src/app/docs/authentication/authentication-content.mdx');
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error('Error loading authentication content:', error);
    return '';
  }
}

export default async function AuthenticationPage() {
  const content = await getAuthContent();
  return (
    <div className="docs-content">
      <Mdx source={content} />
    </div>
  );
}
