# Availability API

The Availability API allows you to check delivery availability for addresses, find lockers near a postal code, and get delivery slot information.

## Overview

The Availability API is a crucial part of the Instabee platform that allows you to:

1. Validate if a postal code is serviceable
2. Get available delivery slots for an address
3. Find lockers near a postal code or by country
4. Get all serviceable postal codes by country
5. Filter options by brand, dates, and other preferences

Instabee Availability API will utilise the same structure for both lockers and home delivery but there will of course be some differences. The new API will be a "layer on top" of the current stack.

## Key Concepts

### Availability Token

Most of the Availability API endpoints return an **availability token** that you must save and use when creating a parcel. This token contains essential information about the selected delivery option and is valid for 24 hours.

### Postal Code Validation

Validating a postal code is the first step in determining if Instabee can provide delivery services to a particular area. Not all postal codes are serviceable - this can depend on the delivery type (home vs locker) and brand.

### Delivery Windows

For home delivery services, the API returns time slots (delivery windows) when a delivery can be made. These slots typically include:
- Date (YYYY-MM-DD format)
- Start time (HH:MM format)  
- End time (HH:MM format)

## Endpoints

| Name | Endpoint | Method | Description |
|------|----------|--------|-------------|
| Get Locker Per Postal Code | `api.instabee.com/availability/postal-codes/\{postalCode\}/lockers` | POST | Find lockers near a specific postal code |
| Get Locker Per Country | `api.instabee.com/availability/countries/\{countryCode\}/lockers` | GET | Get all lockers in a specific country |
| Validate Postal Code or Get Delivery Slots | `api.instabee.com/availability/postal-codes/\{postalCode\}/home-deliveries` | POST | Check if a postal code is serviceable for home delivery and get time slots |
| Get Postal Codes Per Country | `api.instabee.com/availability/countries/\{countryCode\}/home-delivery-postal-codes` | GET | Get all serviceable postal codes for a country |

## Request Parameters

The Availability API accepts various parameters to customize the delivery options returned. Here's a comprehensive reference of all available parameters.

### Query Parameters

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>brand</code></td>
<td>string</td>
<td>optional</td>
<td>Define brand of the delivery options</td>
<td><code>instabox</code> or <code>budbee</code></td>
<td>N/A</td>
<td></td>
</tr>
<tr>
<td><code>product</code></td>
<td>string</td>
<td>optional</td>
<td>Define the product of the delivery options</td>
<td>Will be defined by tech sales in the integration process</td>
<td></td>
<td>The idea is to use this for SME setups in the future</td>
</tr>
<tr>
<td><code>countryCode</code></td>
<td>string</td>
<td>required*</td>
<td>Recipient country code. *Can be sent in the body instead</td>
<td>ISO 3166 alpha 2</td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>deliverySlotCount</code></td>
<td>number</td>
<td>optional</td>
<td>Define the upcoming number of delivery slots to be returned</td>
<td>Minimum 5, Maximum 10</td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>deliverySlotsByDate</code></td>
<td>string</td>
<td>optional</td>
<td>Define specific dates to receive delivery slots between. "2025-06-01,2025-06-08"</td>
<td>Maximum delivery options will be 10. ISO-8601-date YYYY-MM-DD</td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>email</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient email address</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>address</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient address</td>
<td><code>%20</code> should be used for spaces</td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>city</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient city</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

### Request Body

The Availability API accepts a JSON request body with detailed information about the recipient, cart, and delivery preferences.

#### Root Parameters

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>brandId</code></td>
<td>string</td>
<td>optional</td>
<td>Brand identifier</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>sender</code></td>
<td>object</td>
<td>optional</td>
<td>Sender information for C2C products</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>recipient</code></td>
<td>object</td>
<td>required</td>
<td>Recipient information</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>dispatch</code></td>
<td>object</td>
<td>optional</td>
<td>Dispatch information</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>options</code></td>
<td>object</td>
<td>optional</td>
<td>API response options</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>cart</code></td>
<td>object</td>
<td>optional</td>
<td>Cart and parcel information</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### sender

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td>string</td>
<td>optional</td>
<td>Sender name</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>email</code></td>
<td>string</td>
<td>optional</td>
<td>Sender email address</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>phone</code></td>
<td>string</td>
<td>optional</td>
<td>Sender phone number</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>street</code></td>
<td>string</td>
<td>optional</td>
<td>Sender street address</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>street2</code></td>
<td>string</td>
<td>optional</td>
<td>Additional sender street information</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>postalCode</code></td>
<td>string</td>
<td>optional*</td>
<td>Sender postal code. *Required for C2C product</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>city</code></td>
<td>string</td>
<td>optional</td>
<td>Sender city</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>countryCode</code></td>
<td>string</td>
<td>optional*</td>
<td>Sender country code. *Required for C2C product</td>
<td>ISO 3166 alpha 2</td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>coordinates</code></td>
<td>object</td>
<td>optional</td>
<td>Sender coordinates</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### sender.coordinates

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>long</code></td>
<td>number</td>
<td>optional</td>
<td>Sender longitude geolocation</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>lat</code></td>
<td>number</td>
<td>optional</td>
<td>Sender latitude geolocation</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### recipient

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient name</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>email</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient email address</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>phone</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient phone number</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>street</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient street address</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>street2</code></td>
<td>string</td>
<td>optional</td>
<td>Additional recipient street information</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>postalCode</code></td>
<td>string</td>
<td>required</td>
<td>Recipient postal code</td>
<td></td>
<td></td>
<td>Also query param</td>
</tr>
<tr>
<td><code>city</code></td>
<td>string</td>
<td>optional</td>
<td>Recipient city</td>
<td></td>
<td></td>
<td>Also query param</td>
</tr>
<tr>
<td><code>countryCode</code></td>
<td>string</td>
<td>required*</td>
<td>Recipient country code</td>
<td>ISO 3166 alpha 2</td>
<td></td>
<td>Also query param</td>
</tr>
<tr>
<td><code>coordinates</code></td>
<td>object</td>
<td>optional</td>
<td>Recipient coordinates</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### recipient.coordinates

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>long</code></td>
<td>number</td>
<td>optional</td>
<td>Recipient longitude geolocation</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>lat</code></td>
<td>number</td>
<td>optional</td>
<td>Recipient latitude geolocation</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### dispatch

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>readyToShip</code></td>
<td>string</td>
<td>optional</td>
<td>When the parcel is ready to ship</td>
<td>ISO-8601 datetime</td>
<td></td>
<td>Also called readyToPack or OutOfStock or DoNotDeliverBefore</td>
</tr>
<tr>
<td><code>collectionPointId</code></td>
<td>string</td>
<td>optional</td>
<td>Collection point identifier</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### options

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>responseFields</code></td>
<td>object</td>
<td>optional</td>
<td>Define which fields to include in response</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>languageCode</code></td>
<td>string</td>
<td>optional</td>
<td>Response language code</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### options.responseFields

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>directions</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include directions to locker</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td>Can be controlled by partner setting</td>
</tr>
<tr>
<td><code>openhours</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include opening hours information</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>distance</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include distance information</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>lockerAddress</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include locker address</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>coordinates</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include coordinate information</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>price</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include price information</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>localEtas</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include local ETA information</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>estimatedParcelType</code></td>
<td>boolean</td>
<td>optional</td>
<td>Include estimated parcel type</td>
<td><code>true</code>/<code>false</code></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### cart

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Required</th>
<th>Description</th>
<th>Constraints</th>
<th>Enum</th>
<th>Comment</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>checkoutId</code></td>
<td>string</td>
<td>optional</td>
<td>The checkout session ID</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>orderNumber</code></td>
<td>string</td>
<td>optional</td>
<td>The merchants order number</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>totalValueInCents</code></td>
<td>number</td>
<td>optional</td>
<td>Total value of the cart in cents</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>totalWeightGram</code></td>
<td>number</td>
<td>optional</td>
<td>Total weight of the cart in grams</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td><code>parcel</code></td>
<td>object</td>
<td>optional</td>
<td>Parcel details for the cart</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

#### cart.parcel

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **heightCm** | number | optional | Height of the parcel in cm | | | |
| **widthCm** | number | optional | Width of the parcel in cm | | | |
| **lengthCm** | number | optional | Length of the parcel in cm | | | |
| **heightMm** | number | optional | Height of the parcel in mm | | | |
| **widthMm** | number | optional | Width of the parcel in mm | | | |
| **lengthMm** | number | optional | Length of the parcel in mm | | | |
| **volumeDm3** | number | optional | Volume of the parcel in liters | | | |
| **estimatedSize** | string | optional | Estimated size of the parcel | | `SMALL`, `MEDIUM`, `LARGE` | |
| **weightGram** | number | optional | Weight of the parcel in grams | | | |
| **type** | string | optional | Type of parcel | | `BOX`, `ENVELOPE` | |
| **products** | array | optional | List of products in the parcel | | | |

#### cart.parcel.products

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **name** | string | optional | Name of the product | | | |
| **quantity** | string | optional | Quantity of the product | | | |
| **productId** | string | optional | The unique identifier of the product e.g EAN number | | | Not to be confused with (product) serial number |
| **details** | object | optional | Details of the product | | | |

#### cart.parcel.products.details

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **productType** | string | optional | Type of product, e.g Toys | | | |
| **imgUrl** | string | optional | An URL link to the image of the product | | | |
| **category** | string | optional | The category of the product e.g Plushie | | | |
| **brand** | string | optional | The brand of the product | | | |
| **description** | string | optional | The description of the product | | | |
| **price** | object | optional | The price of the product for a single item | | | |
| **temperature** | object | optional | Temperature constraints of the product | | | |

#### cart.parcel.products.details.price

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **priceInCents** | number | optional | Price of a single item in cents without discount | | | |
| **taxRateInCents** | number | optional | The tax rate of the item in cents | | | % |
| **discountRateInCents** | number | optional | The active discount set on the product in cents | | | |
| **currency** | string | optional | The currency of the checkout session | | | |

#### cart.parcel.products.details.temperature

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **min** | number | optional | The minimum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius | Min 8- Max 25 | | |
| **max** | number | optional | The maximum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius | | | |

#### cart.parcel.products.packages

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **heightMm** | string | optional | The height of the individual product package in mm | | | |
| **widthMm** | string | optional | The width of the individual product package in mm | | | |
| **lengthMm** | string | optional | The length of the individual product package in mm | | | |
| **weightGram** | string | optional | The weight of the individual product package in gram | | | |
| **volumeDm3** | string | optional | The volume of the individual product package in liters | | | |
| **barcodes** | object | optional | The barcodes of the individual product | | | |

#### cart.parcel.products.packages.barcodes

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------|------|----------|-------------|-------------|------|---------|
| **code** | string | optional | The content of the barcode e.g 123-456-789 | | | |
| **type** | string | optional | The barcode type e.g EAN13 | | | |

## Response Parameters

The Availability API returns comprehensive information about available delivery options, including lockers and home delivery slots.

### Common Response Fields

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>showAsOptionInCheckout</code></td>
<td>boolean</td>
<td>Whether this option should be shown in checkout</td>
</tr>
<tr>
<td><code>availabilityToken</code></td>
<td>string</td>
<td>Token to use when creating a parcel (valid for 24 hours)</td>
</tr>
<tr>
<td><code>checkoutId</code></td>
<td>string</td>
<td>The checkout session ID</td>
</tr>
<tr>
<td><code>responseExpiresAt</code></td>
<td>string</td>
<td>When this response expires (ISO-8601 datetime)</td>
</tr>
<tr>
<td><code>preselection</code></td>
<td>string</td>
<td>Recommended preselection option</td>
</tr>
<tr>
<td><code>estimatedParcelType</code></td>
<td>string</td>
<td>Estimated parcel type based on cart contents</td>
</tr>
<tr>
<td><code>deliveryOptions</code></td>
<td>array</td>
<td>List of available delivery options</td>
</tr>
</tbody>
</table>

### Locker Delivery Response

#### deliveryOptions (Locker)

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>labelCode</code></td>
<td>string</td>
<td>Label code for the locker</td>
</tr>
<tr>
<td><code>sortCode</code></td>
<td>string</td>
<td>Enhanced sorting code for the locker</td>
</tr>
<tr>
<td><code>type</code></td>
<td>string</td>
<td>Type of delivery option (always "locker")</td>
</tr>
<tr>
<td><code>brand</code></td>
<td>string</td>
<td>Brand of the locker (e.g., "Instabox", "Budbee")</td>
</tr>
<tr>
<td><code>deliveryOption</code></td>
<td>string</td>
<td>Name of the delivery option</td>
</tr>
<tr>
<td><code>deliveryOptionWithEta</code></td>
<td>string</td>
<td>Delivery option with ETA information</td>
</tr>
<tr>
<td><code>eta</code></td>
<td>object</td>
<td>Estimated time of arrival information</td>
</tr>
<tr>
<td><code>localEta</code></td>
<td>object</td>
<td>Localized ETA information</td>
</tr>
<tr>
<td><code>consumerCutoffDatetime</code></td>
<td>string</td>
<td>Latest time consumer can select this option</td>
</tr>
<tr>
<td><code>packBeforeDatetime</code></td>
<td>string</td>
<td>Latest time to pack before pickup</td>
</tr>
<tr>
<td><code>directions</code></td>
<td>object</td>
<td>Directions to find the locker</td>
</tr>
<tr>
<td><code>address</code></td>
<td>object</td>
<td>Address information for the locker</td>
</tr>
<tr>
<td><code>deliveryPrice</code></td>
<td>object</td>
<td>Price information for delivery</td>
</tr>
<tr>
<td><code>distance</code></td>
<td>object</td>
<td>Distance information from recipient</td>
</tr>
<tr>
<td><code>openHours</code></td>
<td>array</td>
<td>Opening hours information</td>
</tr>
<tr>
<td><code>consolidation</code></td>
<td>boolean</td>
<td>Whether consolidation is available</td>
</tr>
</tbody>
</table>

#### eta

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>certainty</code></td>
<td>string</td>
<td>Certainty level of ETA (<code>EXACT</code>, <code>INTERVAL</code>, <code>UNCERTAIN</code>)</td>
</tr>
<tr>
<td><code>etaExact</code></td>
<td>string</td>
<td>Exact ETA datetime (ISO-8601)</td>
</tr>
<tr>
<td><code>etaInterval</code></td>
<td>object</td>
<td>ETA interval when exact time is not available</td>
</tr>
</tbody>
</table>

#### eta.etaInterval

| Parameter | Type | Description |
|-----------|------|-------------|
| **from** | string | Start of ETA interval (ISO-8601 datetime) |
| **to** | string | End of ETA interval (ISO-8601 datetime) |

#### localEta

| Parameter | Type | Description |
|-----------|------|-------------|
| **date** | string | Local date of delivery (YYYY-MM-DD) |
| **dayText** | string | Human-readable day text (e.g., "today", "2-3 days") |
| **etaTimeExact** | string | Exact time in local format (HH:MM) |

#### directions

| Parameter | Type | Description |
|-----------|------|-------------|
| **short** | string | Brief directions to the locker |
| **long** | string | Detailed directions to the locker |

#### address

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>street</code></td>
<td>string</td>
<td>Street address of the locker</td>
</tr>
<tr>
<td><code>postalCode</code></td>
<td>string</td>
<td>Postal code of the locker</td>
</tr>
<tr>
<td><code>city</code></td>
<td>string</td>
<td>City of the locker</td>
</tr>
<tr>
<td><code>countryCode</code></td>
<td>string</td>
<td>Country code (ISO 3166 alpha 2)</td>
</tr>
<tr>
<td><code>country</code></td>
<td>string</td>
<td>Full country name</td>
</tr>
<tr>
<td><code>coordinates</code></td>
<td>object</td>
<td>Geographic coordinates</td>
</tr>
</tbody>
</table>

#### address.coordinates

| Parameter | Type | Description |
|-----------|------|-------------|
| **lat** | number | Latitude coordinate |
| **long** | number | Longitude coordinate |

#### deliveryPrice

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>priceInCents</code></td>
<td>number</td>
<td>Price in cents</td>
</tr>
<tr>
<td><code>currency</code></td>
<td>string</td>
<td>Currency code (<code>SEK</code>, <code>NOK</code>, <code>DKK</code>, <code>EUR</code>)</td>
</tr>
</tbody>
</table>

#### distance

<table class="parameter-table">
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>unit</code></td>
<td>string</td>
<td>Unit of measurement</td>
</tr>
<tr>
<td><code>value</code></td>
<td>number</td>
<td>Distance value</td>
</tr>
<tr>
<td><code>text</code></td>
<td>string</td>
<td>Human-readable distance text</td>
</tr>
<tr>
<td><code>type</code></td>
<td>string</td>
<td>Type of distance calculation (<code>euclidean</code>, <code>walking</code>, <code>driving</code>)</td>
</tr>
</tbody>
</table>

#### openHours

| Parameter | Type | Description |
|-----------|------|-------------|
| **date** | string | Date in YYYY-MM-DD format |
| **isOpen** | boolean | Whether the location is open |
| **open** | string | Opening time (ISO-8601 datetime) |
| **close** | string | Closing time (ISO-8601 datetime) |
| **textLocal** | string | Localized opening hours text |

### Home Delivery Response

For home delivery requests, the response includes delivery time slots and sender information.

#### Additional Fields for Home Delivery

| Parameter | Type | Description |
|-----------|------|-------------|
| **sender** | object | Sender information |
| **deliveryOptions** | array | Available home delivery slots |

#### sender (Response)

| Parameter | Type | Description |
|-----------|------|-------------|
| **name** | string | Sender name |
| **email** | string | Sender email |
| **phone** | string | Sender phone |
| **street** | string | Sender street |
| **street2** | string | Additional sender address info |
| **city** | string | Sender city |
| **coordinates** | object | Sender coordinates |

#### deliveryOptions (Home Delivery)

| Parameter | Type | Description |
|-----------|------|-------------|
| **sortCode** | string | Sort code for the delivery |
| **sortToken** | string | Sort token identifier |
| **type** | string | Type of delivery (always "homedelivery") |
| **brand** | string | Delivery brand |
| **originCollectionPoint** | string | Origin collection point ID |
| **eta** | object | Delivery ETA information |
| **consumerCutoffDatetimeUtc** | string | Consumer cutoff time (UTC) |
| **packBeforeDatetimeUtc** | string | Pack before time (UTC) |
| **consolidation** | boolean | Consolidation availability |

## Error Handling

The API may return the following errors:

<table class="parameter-table">
<thead>
<tr>
<th>Status Code</th>
<th>Description</th>
<th>Solution</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>400</code></td>
<td>Invalid request parameters</td>
<td>Check your request body parameters</td>
</tr>
<tr>
<td><code>404</code></td>
<td>Postal code not serviceable</td>
<td>Try a different postal code</td>
</tr>
<tr>
<td><code>429</code></td>
<td>Rate limit exceeded</td>
<td>Reduce request frequency or contact support</td>
</tr>
</tbody>
</table>

## Example Usage - Checking Home Delivery Availability

```javascript
const response = await fetch('https://api.instabee.com/v1/availability/postal-codes/12345/home-deliveries', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Instabee-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    recipient: {
      countryCode: 'SE',
      postalCode: '12345',
      city: 'Stockholm',
      street: 'Example Street 123'
    },
    options: {
      language: 'en',
      deliverySlotCount: 7
    }
  })
});

const data = await response.json();

// Save this token for later use when creating a parcel
const availabilityToken = data.availabilityToken;

// Display available delivery slots to the user
const deliverySlots = data.slots.map((slot) => ({
  date: slot.date,
  time: slot.startTime + " - " + slot.endTime,
  slotId: slot.id
}));
```

## Example Usage - Finding Nearby Lockers

```javascript
const response = await fetch('https://api.instabee.com/v1/availability/postal-codes/12345/lockers', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Instabee-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    brand: 'instabox',
    recipient: {
      countryCode: 'SE'
    }
  })
});

const data = await response.json();

// Display locker locations to the user
const lockers = data.lockers.map(locker => ({
  name: locker.name,
  address: locker.address,
  distance: locker.distance,
  sortCode: locker.sortCode,
  coordinates: {
    lat: locker.coordinates.latitude,
    lng: locker.coordinates.longitude
  }
}));
```

## Integration Best Practices

1. **Cache Results When Possible**: If you're displaying multiple lockers, consider caching the results to reduce API calls.

2. **Provide Clear User Feedback**: If a postal code is not serviceable, provide clear feedback to the user with alternative options.

3. **Implement Progressive Enhancement**: Start with postal code validation before requesting full address details.

4. **Error Handling**: Implement robust error handling to manage API failures gracefully.

5. **Test with Multiple Regions**: Different countries may have different delivery options and postal code formats.
