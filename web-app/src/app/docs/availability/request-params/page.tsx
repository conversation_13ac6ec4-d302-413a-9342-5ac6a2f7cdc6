import { Metadata } from "next";
import { Mdx } from "@/components/mdx/mdx-components";
import fs from 'fs';
import path from 'path';

export const metadata: Metadata = {
  title: "Availability Request Parameters | Instabee API Documentation",
  description: "Request parameters for the Availability API",
}

function getMarkdownContent() {
  try {
    const filePath = path.join(process.cwd(), 'src/app/docs/availability/request-params/availability-request-params-content.mdx');
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error('Error loading availability request params content:', error);
    return `
# Availability Request Parameters

## Endpoints Parameters

| Parameter | Endpoint | Type | Header | Comment |
|--------------------------------|-----------------------------------------------------------------------|------|---------------------------------|---------------------------------|
| Get Locker Per Postal Code | \`api.instabee.com/availability/postal-codes/12345/lockers\` | POST | Auth key, version, content header | |
| Get Locker Per Country | \`api.instabee.com/availability/countries/se/lockers\` | GET | Auth key, version, content header | |
| Validate Postal Code or Get Delivery Slots | \`api.instabee.com/availability/postal-codes/12345/home-deliveries\` | POST | Auth key, version, content header | |
| Get Postal Codes Per Country | \`api.instabee.com/availability/countries/se/home-delivery-postal-codes\` | GET | Auth key, version, content header | |

## Query Parameters

| Parameter | Type | Required | Description | Constraints | Enum | Also query param | Comment |
|---------------------|--------|-----------|---------------------------------------------------------|-----------------------------------------------|------------------------------------|------------------|-----------------------------------------------------|
| **brand** | string | optional | Define brand of the delivery options | \`instabox\` or \`budbee\` | N/A | | |
| **product** | string | optional | Define the product of the delivery options | Will be defined by tech sales in the integration process | | | The idea is to use this for SME setups in the future |
| **countryCode** | string | required* | Recipient country code. *Can be sent in the body instead | ISO 3166 alpha 2 | | | |
| **deliverySlotCount** | number | optional | Define the upcoming number of delivery slots to be returned. | Minimum 5, Maximum 10 | | | |
| **deliverySlotsByDate** | string | optional | Define specific dates to receive delivery slots between. "2025-06-01,2025-06-08" | Maximum delivery options will be 10. ISO-8601-date YYYY-MM-DD | | | |
| **email** | string | optional | Recipient email address | | | | |
| **address** | string | optional | Recipient address | \`%20\` should be used for spaces | | | |
| **city** | string | optional | Recipient city | | | | |

## Request Body

| Parameter | Type | Required | Description | Constraints | Enum | Also query param | Comment |
|---------------|--------|-----------|------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------|------|------------------|-----------------------------------------------------|
| **brandId** | string | optional | Can we used to define different brands on the same set of credentials | Based on configuration, if no configuration is set then it will default to main brand on the account | N/A | N/A | |
| **sender** | object | optional | Mainly used for C2C sender information. Partner sender information is not currently used | | | No | The idea is to use this for SME setups in the future |
| **recipient** | object | required* | Recipient information. *Can be sent as a query param instead | | | Yes, some params | |
| **dispatch** | object | optional | Define when a parcel will be ready for pickup. And where it will be packed from, if partner uses multiple warehouses | | | No | |
| **options** | object | optional | Define what information should be returned in the response. Also possible to set language. | | | No | |
| **cart** | object | optional | Send information about the parcel and/or products in the parcel | | | No | |

### sender

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-------------|--------|-----------|------------------------------------------------|-----------------------------------|----------------------|----------------------------------------|
| **name** | string | optional | Sender name | | | |
| **email** | string | optional | Sender email address | | | |
| **phone** | string | optional | Sender mobile phone number | Min: 6 digits, Max 15 digits | | |
| **street** | string | optional | Sender street name and number | | | |
| **street2** | string | optional | Additional sender street information | | | |
| **postalCode** | string | optional* | Sender postalCode. *Is required for C2C product | | | |
| **city** | string | optional | Sender city | | | |
| **countryCode** | string | optional* | Sender country code. *Is required for C2C product | ISO 3166 alpha 2 | | |
| **coordinates** | object | optional | Coordinates of the sender location (address) | | | |

#### sender.coordinates

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|---------|--------|-----------|-----------------------------|-------------|------|--------|
| **long** | number | optional | Sender longitude geolocation | | | |
| **lat** | number | optional | Sender latitude geolocation | | | |

### recipient

| Parameter | Type | Required | Description | Constraints | Enum | Also query param | Comment |
|-------------|--------|-----------|------------------------------------------------|------------------|----------------------|------------------|--------|
| **name** | string | optional | Recipient name | | | No | |
| **email** | string | optional | Recipient email | | | Yes | |
| **phone** | string | optional | Recipient mobile phone number | | | No | |
| **street** | string | optional | Recipient address | | | Yes | |
| **street2** | string | optional | Recipient additional address information | | | No | |
| **postalCode** | string | required* | Recipient postal code | | | Yes | |
| **city** | string | optional | Recipient city | | | Yes | |
| **countryCode** | string | required* | Recipient country code | ISO 3166 alpha 2 | | Yes | |
| **coordinates** | object | optional | Recipient coordinates (address) | | | No | |

#### recipient.coordinates

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|---------|--------|-----------|---------------------------------|-------------|------|--------|
| **long** | number | optional | Recipient longitude geolocation | | | |
| **lat** | number | optional | Recipient latitude geolocation | | | |

### dispatch

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|------------------|---------|-----------|--------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------|------------|----------------------------------------------------------------------------|
| **readyToShip** | string | optional | When the parcel is ready for pickup. Instabee will look for the next pickup time based on this datetime | ISO-8601-date Only one date of ready to ship/pack and out of stock can be used | | |
| **readyToPack** | string | optional | When the parcels packing process will be initiated. Instabee will add packing time to this datetime | ISO-8601-date Only one date of ready to ship/pack and out of stock can be used | | |
| **outOfStock** | boolean | optional | One of the products in the checkout is out of stock. This will remove the ETA from the delivery options. Parcel can be packed whenever in the future. | Only one date of ready to ship/pack and out of stock can be used | \`true\`/\`false\` | |
| **doNotDeliverBefore** | string | optional | The date Instabee can't deliver before. The ETA in the checkout will always be on or after the datetime sent | ISO-8601-date | | |
| **packingTime** | number | optional | The number of minutes of packing time that is required for the items in the checkout | | | |
| **collectionPointId** | string | optional | This is to define a specific warehouse the products will be sent from. The warehouse ID will be defined by configuration on Instabee side | | | |

### options

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|------------------|--------|-----------|------------------------------------------------------------------------------------------------------------|-------------|-----------|-------------------------------------------------------------------------|
| **responseFields** | object | optional | Define which information should be returned in the Availability API response | | | |
| **languageCode** | string | optional | Define the language of e.g descriptions and local term of the response. If omitted, this will default to the main language based on the countryCode | ISO 639-1 | | |

#### options.responseFields

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-----------------------|---------|-----------|-------------------------------------------------------------------------------------------------------------------------------------------|----------------|--------------|-----------------------------------|
| **directions** | boolean | optional | If set to true the instructions how the user find the locker will be returned in the Availability API response | \`true\`/\`false\` | | Can be controlled by partner setting |`;
  }
}

export default async function AvailabilityRequestParamsPage() {
  const content = await getMarkdownContent();

  return (
    <div className="space-y-4">
      <div className="mdx max-w-full break-words">
        <Mdx source={content} />
      </div>
    </div>
  );
}
