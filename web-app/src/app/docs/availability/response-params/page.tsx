import { Metadata } from "next";
import { Mdx } from "@/components/mdx/mdx-components";
import path from 'path';
import fs from 'fs';

export const metadata: Metadata = {
  title: "Availability Response Parameters | Instabee API Documentation",
  description: "Response parameters from the Availability API",
}

function getMarkdownContent() {
  try {
    const filePath = path.join(process.cwd(), 'src/app/docs/availability/response-params/availability-response-params-content.mdx');
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error('Error loading availability response params content:', error);
    return `# Availability Response Parameters

Error loading content. Please check the file path.`;
  }
}

export default async function AvailabilityResponseParamsPage() {
  const content = await getMarkdownContent();

  return (
    <div className="space-y-4">
      <div className="mdx max-w-full break-words">
        <Mdx source={content} />
      </div>
    </div>
  );
}

