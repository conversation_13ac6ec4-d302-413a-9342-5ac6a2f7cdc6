import { Metadata } from "next";
import { Mdx } from "@/components/mdx/mdx-components";
import fs from 'fs';
import path from 'path';

export const metadata: Metadata = {
  title: "Post Packing | Instabee API Documentation",
  description: "Update parcels after packing",
}

async function getMarkdownContent() {
  try {
    const filePath = path.join(process.cwd(), 'src/app/docs/post-packing/post-packing-content.mdx');
    const content = fs.readFileSync(filePath, 'utf8');

    // Process content to make it work well with our MDX renderer
    return content
      .replace(/^````markdown$/gm, '') // Remove markdown code block indicators
      .replace(/^````$/gm, '') // Remove end of code block indicators
      .replace(/\/PUT/g, '`PUT`'); // Format PUT as inline code
  } catch (error) {
    console.error("Error reading markdown file:", error);
    return '# Error loading post packing documentation';
  }
}

export default async function PostPackingPage() {
  const content = await getMarkdownContent();

  return (
    <div className="docs-content">
      <Mdx source={content} />
    </div>
  );
}
