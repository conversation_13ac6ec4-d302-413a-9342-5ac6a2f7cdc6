# Create Parcel API (Post Purchase)

The Create Parcel API (also known as Create Parcel or Order Create API) is used to register a parcel for delivery immediately after a customer completes their purchase. This is the prebooking step that reserves capacity for delivery before the parcel is physically packed.

## Overview

**Post Purchase vs Post Packing**: The Post Purchase flow is like a prebooking - it reserves delivery capacity and creates the initial order. Merchants can send information in different steps, with the Post Packing flow being the final confirmation after physical packing.

This endpoint should be called after a customer has:
1. Selected a delivery option (using the Availability API)
2. Completed their purchase in your checkout system

You can provide information at different levels:
- **Bare Minimum**: Nothing (if all information was provided in availability call)
- **Minimum**: Basic order identification and delivery option
- **Regular**: Basic recipient and delivery information
- **Maximum**: Complete order details including products, dimensions, and special services

## API Endpoint

| Endpoint | Method | Content-Type |
|----------|--------|-------------|
| `api.instabee.com/orders` | PUT | application/json |

## Integration Examples

### Bare Minimum
**Nothing** - If all information was provided in the availability call, no additional call is needed.

### Minimum
This is the bare minimum we need to be able to at least reserve capacity for the delivery before the parcel is packed and ready.

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190", // OneOf this, or checkoutId, or orderNumber
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "cart": { // OneOf this, or orderNumber, or availailityToken
    "orderNumber": "12345" // OneOf this, or checkoutId, or availailityToken
  },
  "deliveryOption": {
    "sort_code": "IN123"
  }
}
```

### Regular
This is what we expect to get in the majority of cases even though we will aim to get more information:

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190", // OneOf this, or checkoutId, or orderNumber
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelId": "PREFIX1234567890", // Can be left out
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567", //can be changed to optional with setting
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "countryCode": "SE"
  },
   "deliveryOption": {
    "sort_code": "IN123"
  },
   "cart": { // OneOf this, or orderNumber, or availailityToken
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345" // OneOf this, or checkoutId, or availailityToken
  }
}
```

### Core Parameters

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **availabilityToken** | string | required* | Token from the availability check | OneOf this, checkoutId, or orderNumber | Links to availability check |
| **brand** | string | required | Brand for the delivery | "instabox" or "budbee" | Determines delivery network |
| **product** | string | required | Product type for the delivery | "LOCKER_EXPRESS", "HOME_DELIVERY", etc. | Defines service level |
| **parcelId** | string | optional | Your reference ID for the parcel | Can be auto-generated if omitted | Used for your own tracking |
| **parcelGroupId** | string | optional | Groups multiple parcels together | | For multi-parcel orders |
| **brandId** | string | optional | Your brand identifier | | For multi-brand merchants |
| **communicationName** | string | optional | Name shown in customer communications | | Overrides default brand name |
| **parcelPackingConfirmed** | boolean | optional | Set to true if parcel is already packed | Default: false | Can be used for immediate confirmation |

### Recipient Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **recipient.name** | string | required | Recipient name | | |
| **recipient.email** | string | required | Recipient email address | Valid email format | Used for delivery notifications |
| **recipient.phone** | string | required | Recipient phone number | Min: 6 digits, Max 15 digits | Can be made optional with setting |
| **recipient.ssn** | string | optional | Social security number | | For age verification services |
| **recipient.street** | string | required | Recipient street address | | |
| **recipient.street2** | string | optional | Additional address information | | Floor, apartment, etc. |
| **recipient.postalCode** | string | required | Recipient postal code | | |
| **recipient.city** | string | required | Recipient city | | |
| **recipient.countryCode** | string | required | Recipient country code | ISO 3166 alpha 2 | e.g., "SE", "DK", "NO" |
| **recipient.coordinates** | object | optional | GPS coordinates | | For precise location |
| **recipient.coordinates.lat** | number | optional | Latitude | | |
| **recipient.coordinates.long** | number | optional | Longitude | | |

### Sender Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **sender.name** | string | optional | Sender/merchant name | | |
| **sender.email** | string | optional | Sender email address | Valid email format | |
| **sender.phone** | string | optional | Sender phone number | | |
| **sender.street** | string | optional | Sender street address | | |
| **sender.street2** | string | optional | Additional sender address info | | |
| **sender.postalCode** | string | optional | Sender postal code | | |
| **sender.city** | string | optional | Sender city | | |
| **sender.countryCode** | string | optional | Sender country code | ISO 3166 alpha 2 | |
| **sender.coordinates** | object | optional | Sender GPS coordinates | | |
| **sender.coordinates.lat** | number | optional | Latitude | | |
| **sender.coordinates.long** | number | optional | Longitude | | |

### Delivery Options

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **deliveryOption.sort_code** | string | required for locker | Sort code for the locker | | Identifies specific locker |
| **deliveryOption.slotId** | string | required for home delivery | ID of the selected delivery slot | | Obtained from availability check |
| **deliveryOption.etaInterval** | object | optional | Preferred delivery time window | | |
| **deliveryOption.etaInterval.from** | string | optional | Start of delivery window | ISO-8601 datetime | |
| **deliveryOption.etaInterval.to** | string | optional | End of delivery window | ISO-8601 datetime | Will default to best available if doesn't match |

### Dispatch Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **dispatch.readyToShip** | string | optional | When parcel is ready for pickup | ISO-8601 datetime | Only one of readyToShip/readyToPack/outOfStock |
| **dispatch.readyToPack** | string | optional | When packing process will start | ISO-8601 datetime | Instabee adds packing time to this |
| **dispatch.outOfStock** | boolean | optional | Product is out of stock | true/false | Removes ETA from delivery options |
| **dispatch.doNotDeliverBefore** | string | optional | Earliest delivery date allowed | ISO-8601 datetime | ETA will be on or after this date |
| **dispatch.packingTime** | number | optional | Required packing time in minutes | | |
| **dispatch.collectionPointId** | string | optional | Specific warehouse/pickup point | | Configured by Instabee |
| **dispatch.returnPointId** | string | optional | Return address point ID | | For failed deliveries |

### Options and Settings

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **options.languageCode** | string | optional | Language for customer communications | ISO 639-1 | e.g., "EN", "SE", "NO" |
| **options.localEtas** | boolean | optional | Return local delivery terms | true/false | Different local terms for delivery promise |
| **options.estimatedParcelType** | boolean | optional | Return estimated parcel size | true/false | Calculated based on cart contents |

### Delivery Instructions

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **deliveryInstructions.notifyBy** | string | optional | How to notify recipient | "ring_doorbell", "knock_on_door" | |
| **deliveryInstructions.doorCode** | string | optional | Door or building access code | | |
| **deliveryInstructions.message** | string | optional | Special delivery instructions | | Free text message |
| **deliveryInstructions.intercom** | boolean | optional | Use intercom if available | true/false | |

### Additional Services

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **additionalServices.identification** | object | optional | Identity verification requirements | | |
| **additionalServices.identification.type** | string | optional | Type of verification | "age_limit", "age_limit_at_handover", "specific_person", "any_person" | |
| **additionalServices.identification.ageLimit** | number | optional | Minimum age required | | Used with age_limit types |
| **additionalServices.identification.ssn** | string | optional | Required SSN for verification | | |
| **additionalServices.identification.name** | string | optional | Required name for verification | | |
| **additionalServices.leaveByDoor** | string | optional | Allow leaving parcel by door | "allow", "disallow", "force" | |
| **additionalServices.leaveWithNeighbour** | string | optional | Allow leaving with neighbor | "allow", "disallow", "force" | |
| **additionalServices.numberOfMissRetries** | number | optional | Number of delivery retry attempts | Minimum: 1 | null = default |

### Cart Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.checkoutId** | string | optional | Checkout session ID | OneOf this, orderNumber, or availabilityToken | |
| **cart.orderNumber** | string | required* | Your order number | OneOf this, checkoutId, or availabilityToken | Used to link with your system |
| **cart.totalValueInCents** | number | optional | Total order value in cents | | Used for insurance purposes |
| **cart.totalWeightGram** | number | optional | Total weight in grams | | For logistics planning |
| **cart.parcel** | object | optional | Parcel dimensions and contents | | Detailed parcel information |

### Parcel Details

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.heightCm** | number | optional | Parcel height in cm | | |
| **cart.parcel.widthCm** | number | optional | Parcel width in cm | | |
| **cart.parcel.lengthCm** | number | optional | Parcel length in cm | | |
| **cart.parcel.volumeDm3** | number | optional | Parcel volume in dm³ | | |
| **cart.parcel.estimatedSize** | string | optional | Estimated size category | "small", "medium", "large" | |
| **cart.parcel.weightGram** | number | optional | Parcel weight in grams | | |
| **cart.parcel.type** | string | optional | Parcel type | "box", "envelope", "bag" | |
| **cart.parcel.products** | array | optional | Array of products in parcel | | Detailed product information |

### Product Details

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.products[].name** | string | optional | Product name | | |
| **cart.parcel.products[].quantity** | number | optional | Quantity of this product | | |
| **cart.parcel.products[].productId** | string | optional | Your product identifier | | |
| **cart.parcel.products[].details** | object | optional | Detailed product information | | |
| **cart.parcel.products[].details.productType** | string | optional | Type of product | e.g., "Prescription" | |
| **cart.parcel.products[].details.imgUrl** | string | optional | Product image URL | | |
| **cart.parcel.products[].details.category** | string | optional | Product category | | |
| **cart.parcel.products[].details.brand** | string | optional | Product brand | | |
| **cart.parcel.products[].details.description** | string | optional | Product description | | |
| **cart.parcel.products[].details.price** | object | optional | Price information | | |
| **cart.parcel.products[].details.price.priceInCents** | number | optional | Price in cents | | |
| **cart.parcel.products[].details.price.taxRateInCents** | number | optional | Tax amount in cents | | |
| **cart.parcel.products[].details.price.discountRateInCents** | number | optional | Discount amount in cents | | |
| **cart.parcel.products[].details.price.currency** | string | optional | Currency code | ISO 4217 | e.g., "SEK" |
| **cart.parcel.products[].details.temperature** | object | optional | Temperature requirements | | |
| **cart.parcel.products[].details.temperature.min** | number | optional | Minimum temperature in Celsius | | |
| **cart.parcel.products[].details.temperature.max** | number | optional | Maximum temperature in Celsius | | |

### Package and Barcode Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.products[].packages** | array | optional | Physical package information | | |
| **cart.parcel.products[].packages[].widthMm** | number | optional | Package width in mm | | Note: Different from Post Packing (widthCm) |
| **cart.parcel.products[].packages[].heightMm** | number | optional | Package height in mm | | Note: Different from Post Packing (heightCm) |
| **cart.parcel.products[].packages[].lengthMm** | number | optional | Package length in mm | | Note: Different from Post Packing (lengthCm) |
| **cart.parcel.products[].packages[].weightMm** | number | optional | Package weight | | Note: Different unit from Post Packing |
| **cart.parcel.products[].packages[].volumeMm3** | number | optional | Package volume in mm³ | | Note: Different from Post Packing (volumeDm3) |
| **cart.parcel.products[].packages[].barcodes** | array | optional | Barcode information | | |
| **cart.parcel.products[].packages[].barcodes[].code** | string | optional | Barcode value | | |
| **cart.parcel.products[].packages[].barcodes[].type** | string | optional | Barcode type | "ean13", etc. | Lowercase in Post Purchase |

## Example Request - Bare Minimum (Locker)

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "cart": {
    "orderNumber": "12345"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  }
}
```

## Example Request - Regular (Locker)

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelId": "PREFIX1234567890",
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345"
  }
}
```

## Example Request - Maximum (Complete Order)

```json
{
  "parcelPackingConfirmed": true,
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelGroupId": "123456789",
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelId": "PREFIX1234567890",
  "brandId": "Brand1",
  "communicationName": "That Merchant Name",
  "sender": {
    "name": "Merchant A",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "recipient": {
    "name": "Test Testsson",
    "ssn": "199004152012",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "dispatch": {
    "readyToShip": "2025-12-01T01:23:45.678Z",
    "collectionPointId": "STOCKHOLM_12345",
    "returnPointId": "GBG_123"
  },
  "options": {
    "languageCode": "EN"
  },
  "deliveryInstructions": {
    "notifyBy": "ring_doorbell",
    "doorCode": "1234",
    "message": "Hide it under the rock in the back",
    "intercom": true
  },
  "additionalServices": {
    "identification": {
      "type": "age_limit",
      "ageLimit": 18,
      "ssn": "199004152012",
      "name": "John Doe"
    },
    "leaveByDoor": "allow",
    "leaveWithNeighbour": "allow",
    "numberOfMissRetries": 9
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345",
    "totalValueInCents": 12300,
    "totalWeightGram": 30000,
    "parcel": {
      "heightCm": 10,
      "widthCm": 10,
      "lengthCm": 10,
      "volumeDm3": 1,
      "estimatedSize": "small",
      "weightGram": 2000,
      "type": "box",
      "products": [
        {
          "name": "My Little Pony Deathmetal Limited Edition",
          "quantity": 1,
          "productId": "1234567",
          "details": {
            "productType": "Prescription",
            "imgUrl": "https://tinyurl.com/2p9bu4kz",
            "category": "Toy Horses",
            "brand": "My Little Pony",
            "description": "Let the best worlds of Death Metal and Ponies be combined with this awesome toy",
            "price": {
              "priceInCents": 14900,
              "taxRateInCents": 2500,
              "discountRateInCents": 10,
              "currency": "SEK"
            },
            "temperature": {
              "min": 8,
              "max": 25
            }
          },
          "packages": [
            {
              "widthMm": 5,
              "heightMm": 5,
              "lengthMm": 5,
              "weightMm": 10,
              "volumeMm3": 0.125,
              "barcodes": [
                {
                  "code": "***********",
                  "type": "ean13"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

## Response

A successful request returns a JSON response with:

| Field | Type | Description |
|-------|------|-------------|
| **parcelId** | string | The Instabee parcel ID |
| **trackingNumber** | string | Tracking number for the parcel |
| **trackingUrl** | string | URL to track the parcel |
| **estimatedDelivery** | string | Estimated delivery date (ISO-8601) |
| **status** | string | Current parcel status |

## Example Response - Locker Delivery

```json
{
  "parcelId": "IN123456789",
  "trackingNumber": "IB12345678SE",
  "trackingUrl": "https://tracking.instabee.com/t/IB12345678SE",
  "estimatedDelivery": "2025-05-16",
  "status": "CREATED",
  "deliveryType": "LOCKER",
  "locker": {
    "name": "Mall of Scandinavia",
    "address": "Stjärntorget 2, 169 79 Solna"
  }
}
```

## Example Response - Home Delivery

```json
{
  "parcelId": "HD123456789",
  "trackingNumber": "IB87654321SE",
  "trackingUrl": "https://tracking.instabee.com/t/IB87654321SE",
  "estimatedDelivery": "2025-05-16",
  "deliveryWindow": {
    "date": "2025-05-16",
    "startTime": "18:00",
    "endTime": "22:00"
  },
  "status": "CREATED",
  "deliveryType": "HOME"
}
```

## Error Handling

The API may return the following errors:

| Status Code | Error | Description | Solution |
|-------------|-------|-------------|----------|
| 400 | INVALID_REQUEST | Missing or invalid parameters | Check your request body against the documentation |
| 401 | UNAUTHORIZED | Invalid API key | Verify your API key is correct |
| 404 | NOT_FOUND | Availability token not found | Ensure you're using a valid token from a recent availability check |
| 409 | DUPLICATE_PARCEL | Parcel already exists | Check if this order has already been submitted |
| 422 | VALIDATION_ERROR | Validation errors in request | Fix the specific validation errors returned in the response |

## Best Practices

1. **Use Idempotency Keys**: Add your order number as a reference to avoid duplicate parcels
2. **Handle Webhook Updates**: Register for webhook notifications to receive parcel status updates
3. **Validate Recipient Data**: Ensure all recipient data is valid before submission
4. **Include Detailed Item Information**: Providing detailed cart information improves customer service
5. **Error Handling**: Implement robust error handling for API failures
