@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Documentation-specific styles */
@layer components {
  .docs-content {
    @apply max-w-none;
  }

  .docs-content h1 {
    @apply scroll-m-20 text-4xl font-bold tracking-tight text-amber-500 mb-6;
  }

  .docs-content h2 {
    @apply mt-10 scroll-m-20 border-b border-amber-200/30 pb-2 text-3xl font-semibold tracking-tight first:mt-0 text-amber-500 mb-4;
  }

  .docs-content h3 {
    @apply mt-8 scroll-m-20 text-2xl font-semibold tracking-tight text-amber-600 mb-3;
  }

  .docs-content h4 {
    @apply mt-6 scroll-m-20 text-xl font-semibold tracking-tight text-amber-600 mb-2;
  }

  .docs-content p {
    @apply leading-7 text-muted-foreground mb-4;
  }

  .docs-content ul {
    @apply my-6 ml-6 list-disc space-y-2;
  }

  .docs-content ol {
    @apply my-6 ml-6 list-decimal space-y-2;
  }

  .docs-content li {
    @apply text-muted-foreground;
  }

  .docs-content blockquote {
    @apply mt-6 border-l-2 border-amber-300 pl-6 italic text-muted-foreground;
  }

  .docs-content table {
    @apply w-full border-collapse rounded-lg border border-border shadow-sm my-8;
  }

  .docs-content th {
    @apply px-4 py-3 text-left font-semibold text-amber-600 bg-amber-50/50 border-b border-amber-200/30 text-sm;
  }

  .docs-content td {
    @apply px-4 py-3 text-left text-sm text-muted-foreground border-b border-border/50;
  }

  .docs-content tr:hover {
    @apply bg-amber-50/30 transition-colors;
  }

  .docs-content pre {
    @apply mt-6 mb-6 overflow-x-auto rounded-lg bg-slate-950 p-4 text-sm;
  }

  .docs-content code {
    @apply relative rounded bg-amber-100 text-amber-800 px-[0.3rem] py-[0.2rem] font-mono text-xs font-medium;
  }

  .docs-content pre code {
    @apply bg-transparent p-0 text-slate-50;
  }

  /* Parameter tables with proper styling */
  .docs-content table.parameter-table {
    @apply w-full border-collapse rounded-lg border border-border shadow-sm text-sm my-8;
  }

  .docs-content table.parameter-table thead {
    @apply bg-amber-50/30 border-b-2 border-amber-200/50;
  }

  .docs-content table.parameter-table th {
    @apply px-4 py-3 text-left font-semibold text-amber-600 bg-amber-50/50 border-b border-amber-200/30 text-sm whitespace-normal break-words;
  }

  .docs-content table.parameter-table td {
    @apply px-4 py-3 text-left text-sm text-muted-foreground align-top whitespace-normal break-words border-b border-border/50;
  }

  .docs-content table.parameter-table tr:hover {
    @apply bg-amber-50/30 transition-colors;
  }

  .docs-content table.parameter-table code {
    @apply relative rounded bg-amber-100 text-amber-800 px-[0.3rem] py-[0.2rem] font-mono text-xs font-medium;
  }


}
