'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';

interface TOCItem {
  id: string;
  text: string;
  level: number;
  children: TOCItem[];
}

export function DocsTOC() {
  const [headings, setHeadings] = useState<TOCItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isScrollingProgrammatically, setIsScrollingProgrammatically] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const activeSection = searchParams.get('section');

  // Extract headings from the document
  useEffect(() => {
    const extractHeadings = () => {
      const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const toc: TOCItem[] = [];
      const stack: TOCItem[] = []; // Stack to track parent hierarchy

      headingElements.forEach((el) => {
        const level = parseInt(el.tagName[1]);
        const id = el.id;
        const text = el.textContent || '';

        // Skip headings without IDs or with empty text
        if (!id || !text.trim()) return;

        const item: TOCItem = { id, text, level, children: [] };

        // Remove items from stack that are at same or deeper level
        while (stack.length > 0 && stack[stack.length - 1].level >= level) {
          stack.pop();
        }

        // If stack is empty or this is a top-level heading, add to root
        if (stack.length === 0 || level <= 2) {
          toc.push(item);
        } else {
          // Add as child to the last item in stack
          const parent = stack[stack.length - 1];
          parent.children.push(item);
        }

        // Add current item to stack
        stack.push(item);
      });

      return toc;
    };

    // Extract headings after a delay to ensure content is loaded
    const timeoutId = setTimeout(() => {
      const extractedHeadings = extractHeadings();
      setHeadings(extractedHeadings);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [pathname]);

  // Intersection Observer for scroll-based highlighting (visual only)
  useEffect(() => {
    const headingElements = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).filter(
      (el) => el.id && el.textContent?.trim()
    );

    if (headingElements.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        // Don't update if we're currently scrolling programmatically
        if (isScrollingProgrammatically) return;

        // Find the heading that's most visible
        const visibleEntries = entries.filter((entry) => entry.isIntersecting);

        if (visibleEntries.length > 0) {
          // Sort by intersection ratio and position to find the most prominent heading
          const mostVisible = visibleEntries.reduce((prev, current) => {
            const prevRatio = prev.intersectionRatio;
            const currentRatio = current.intersectionRatio;

            // If ratios are similar, prefer the one higher on the page
            if (Math.abs(prevRatio - currentRatio) < 0.1) {
              return prev.boundingClientRect.top < current.boundingClientRect.top ? prev : current;
            }

            return prevRatio > currentRatio ? prev : current;
          });

          const newActiveId = mostVisible.target.id;
          if (newActiveId !== activeId) {
            setActiveId(newActiveId);

            // If user scrolled away from the URL section, clear it
            if (activeSection && newActiveId !== activeSection) {
              const url = new URL(window.location.href);
              url.searchParams.delete('section');
              window.history.replaceState({}, '', url.toString());
            }
          }
        }
      },
      {
        rootMargin: '-20% 0px -35% 0px',
        threshold: [0, 0.25, 0.5, 0.75, 1],
      }
    );

    headingElements.forEach((el) => observer.observe(el));

    return () => {
      headingElements.forEach((el) => observer.unobserve(el));
    };
  }, [headings, isScrollingProgrammatically, activeId]);

  // Initialize activeId from URL on first load only
  useEffect(() => {
    if (activeSection && !activeId) {
      setActiveId(activeSection);
      const element = document.getElementById(activeSection);
      if (element) {
        setIsScrollingProgrammatically(true);

        // Add a small delay to ensure the page has rendered
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });

          // Reset the flag after scrolling is complete
          setTimeout(() => {
            setIsScrollingProgrammatically(false);
          }, 1500);
        }, 100);
      }
    }
  }, [activeSection, activeId]);

  if (headings.length === 0) {
    return null;
  }

  return (
    <div className="space-y-1">
      <p className="font-medium text-sm text-muted-foreground mb-2">On this page</p>
      <TocItems
        items={headings}
        activeSection={activeId || activeSection}
        pathname={pathname}
        setIsScrollingProgrammatically={setIsScrollingProgrammatically}
      />
    </div>
  );
}

interface TocItemsProps {
  items: TOCItem[];
  activeSection: string | null;
  pathname: string;
  level?: number;
  setIsScrollingProgrammatically: (value: boolean) => void;
}

function TocItems({ items, activeSection, pathname, level = 0, setIsScrollingProgrammatically }: TocItemsProps) {
  const handleClick = (e: React.MouseEvent, itemId: string) => {
    e.preventDefault();

    // Set flag to prevent intersection observer from interfering
    setIsScrollingProgrammatically(true);

    // Update URL with query parameter
    const url = new URL(window.location.href);
    url.searchParams.set('section', itemId);
    window.history.pushState({}, '', url.toString());

    // Scroll to element
    const element = document.getElementById(itemId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });

      // Reset the flag after scrolling is complete
      setTimeout(() => {
        setIsScrollingProgrammatically(false);
      }, 1500);
    }
  };

  return (
    <ul className={cn("pl-3 space-y-1", level === 0 && "pl-0")}>
      {items.map((item, index) => (
        <li key={`${item.id}-${index}`} className="text-sm leading-snug">
          <a
            href={`${pathname}?section=${item.id}`}
            onClick={(e) => handleClick(e, item.id)}
            className={cn(
              "block py-1 text-muted-foreground hover:text-foreground transition-colors cursor-pointer",
              activeSection === item.id && "text-amber-500 font-medium",
              // Adjust indentation based on heading level
              item.level === 2 ? "pl-0" :
              item.level === 3 ? "pl-2" :
              item.level === 4 ? "pl-4" :
              item.level === 5 ? "pl-6" :
              item.level === 6 ? "pl-8" : ""
            )}
          >
            {item.text}
          </a>
          {item.children.length > 0 && (
            <TocItems
              items={item.children}
              activeSection={activeSection}
              pathname={pathname}
              level={level + 1}
              setIsScrollingProgrammatically={setIsScrollingProgrammatically}
            />
          )}
        </li>
      ))}
    </ul>
  );
}
