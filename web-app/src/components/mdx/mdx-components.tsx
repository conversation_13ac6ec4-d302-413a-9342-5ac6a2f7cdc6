import React from "react";
import { MDXRemote } from "next-mdx-remote/rsc";
import { cn } from "@/lib/utils";
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
// Table components removed - using plain HTML elements with CSS styling
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { addIdsToHeadings } from "@/lib/extract-headings";
import { ClientHeading } from "./client-headings";

const components = {
  h1: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={1}
      className={cn(
        "mt-2 text-3xl font-bold tracking-tight text-amber-500 group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  h2: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={2}
      className={cn(
        "mt-8 border-b border-amber-200/30 pb-1 text-2xl font-semibold tracking-tight first:mt-0 text-amber-500 group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  h3: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={3}
      className={cn(
        "mt-6 text-xl font-semibold tracking-tight text-amber-400 group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  h4: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={4}
      className={cn(
        "mt-6 text-lg font-semibold tracking-tight text-amber-400 group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  h5: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={5}
      className={cn(
        "mt-8 text-lg font-semibold tracking-tight group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  h6: ({ id, className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <ClientHeading
      id={id}
      level={6}
      className={cn(
        "mt-8 text-base font-semibold tracking-tight group",
        className
      )}
    >
      {props.children}
    </ClientHeading>
  ),
  a: ({ className, ...props }: React.HTMLAttributes<HTMLAnchorElement>) => (
    <a
      className={cn(
        "font-medium text-blue-600 underline underline-offset-4",
        className
      )}
      {...props}
    />
  ),
  p: ({ className, ...props }: React.HTMLAttributes<HTMLParagraphElement>) => (
    <p
      className={cn("text-sm leading-6 [&:not(:first-child)]:mt-4", className)}
      {...props}
    />
  ),
  ul: ({ className, ...props }: React.HTMLAttributes<HTMLUListElement>) => (
    <ul className={cn("my-4 ml-6 list-disc text-sm", className)} {...props} />
  ),
  ol: ({ className, ...props }: React.HTMLAttributes<HTMLOListElement>) => (
    <ol className={cn("my-4 ml-6 list-decimal text-sm", className)} {...props} />
  ),
  li: ({ className, ...props }: React.HTMLAttributes<HTMLLIElement>) => (
    <li className={cn("mt-1.5 text-sm", className)} {...props} />
  ),
  blockquote: ({ className, ...props }: React.HTMLAttributes<HTMLQuoteElement>) => (
    <blockquote
      className={cn(
        "mt-4 border-l-2 pl-4 italic text-sm [&>*]:text-muted-foreground",
        className
      )}
      {...props}
    />
  ),
  img: ({
    className,
    alt,
    ...props
  }: React.ImgHTMLAttributes<HTMLImageElement>) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img className={cn("rounded-md border", className)} alt={alt} {...props} />
  ),
  hr: ({ ...props }) => <hr className="my-4 md:my-8" {...props} />,
  table: ({ className, ...props }: React.HTMLAttributes<HTMLTableElement> & { class?: string }) => {
    // Handle both className and class attributes (MDX might use either)
    const tableClass = className || (props as any).class;
    const isParameterTable = tableClass?.includes('parameter-table');

    // For parameter tables, let CSS handle the styling directly
    if (isParameterTable) {
      return (
        <table className={cn("parameter-table", tableClass)} {...props}>
          {props.children}
        </table>
      );
    }

    // For all other tables (including Markdown tables), apply parameter-table styling
    // since they're all parameter documentation tables in this context
    return (
      <table className="parameter-table" {...props}>
        {props.children}
      </table>
    );
  },
  thead: ({ className, ...props }: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <thead className={cn(className)} {...props} />
  ),
  tbody: ({ className, ...props }: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody className={cn(className)} {...props} />
  ),
  tr: ({ className, ...props }: React.HTMLAttributes<HTMLTableRowElement>) => (
    <tr className={cn(className)} {...props} />
  ),
  th: ({ className, ...props }: React.HTMLAttributes<HTMLTableCellElement>) => (
    <th className={cn(className)} {...props} />
  ),
  td: ({ className, ...props }: React.HTMLAttributes<HTMLTableCellElement>) => (
    <td className={cn(className)} {...props} />
  ),
  caption: ({ className, ...props }: React.HTMLAttributes<HTMLTableCaptionElement>) => (
    <caption className={cn(className)} {...props} />
  ),
  pre: ({ className, ...props }: React.HTMLAttributes<HTMLPreElement>) => {
    // We handle code blocks with our custom SyntaxHighlighter component
    // This is only used for non-code-block pre elements
    return (
      <pre
        className={cn(
          "mb-4 mt-4 overflow-x-auto rounded-lg border bg-black py-3 max-w-full",
          className
        )}
        {...props}
      />
    );
  },
  code: ({ className, ...props }: React.HTMLAttributes<HTMLElement>) => {
    const match = /language-([\w-]+)/.exec(className || '');
    const language = match ? match[1] : '';
    const isMultiline = (props.children as string)?.includes('\n');
    
    // If it's a multiline code block with a language
    if (isMultiline && language) {
      return (
        <div className="my-4 overflow-hidden rounded-lg border border-amber-200/30">
          <SyntaxHighlighter
            language={language}
            style={vscDarkPlus}
            showLineNumbers={true}
            wrapLines={true}
            customStyle={{
              margin: 0,
              borderRadius: '0.375rem',
              fontSize: '0.75rem',
              backgroundColor: '#1e1e1e',
            }}
            lineNumberStyle={{
              minWidth: '2.5em',
              paddingRight: '1em',
              color: '#6a737d',
              textAlign: 'right',
              borderRight: '1px solid #444',
              userSelect: 'none',
            }}
          >
            {String(props.children).trim()}
          </SyntaxHighlighter>
        </div>
      );
    }
    
    // For inline code
    return (
      <code
        className={cn(
          "relative rounded bg-amber-100 text-amber-800 px-[0.3rem] py-[0.2rem] font-mono text-xs font-medium",
          className
        )}
        {...props}
      />
    );
  },
  ApiCard: ({ title, description, endpoint, method }: { title: string, description: string, endpoint: string, method: string }) => (
    <Card className="my-4">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <span className={cn(
            "px-2 py-1 rounded text-white text-xs font-bold",
            method === "GET" && "bg-blue-500",
            method === "POST" && "bg-green-500",
            method === "PUT" && "bg-yellow-500",
            method === "DELETE" && "bg-red-500"
          )}>
            {method}
          </span>
          <code className="bg-muted p-2 rounded">{endpoint}</code>
        </div>
      </CardContent>
    </Card>
  ),
  ParamTable: ({ title, description, children }: { title: string, description?: string, children: React.ReactNode }) => (
    <div className="my-6">
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      {description && <p className="mb-4 text-muted-foreground">{description}</p>}
      <div className="overflow-x-auto">
        {children}
      </div>
    </div>
  ),
  ParamAccordion: ({ title, children }: { title: string, children: React.ReactNode }) => (
    <Accordion type="single" collapsible className="my-4">
      <AccordionItem value="item-1">
        <AccordionTrigger>{title}</AccordionTrigger>
        <AccordionContent>{children}</AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

interface MdxProps {
  source: string;
}



/**
 * Pre-process markdown tables to ensure they're properly converted to HTML tables
 */
function processMarkdownTables(markdown: string): string {
  // Check if we need to process tables
  if (!markdown.includes('|')) {
    return markdown;
  }
  
  // Split into lines and look for table structures
  const lines = markdown.split('\n');
  let inTable = false;
  let tableStartIndex = -1;
  const tableRanges: [number, number][] = [];
  
  // Find all table ranges
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('|') && line.endsWith('|')) {
      if (!inTable) {
        inTable = true;
        tableStartIndex = i;
      }
    } else if (inTable) {
      inTable = false;
      tableRanges.push([tableStartIndex, i - 1]);
    }
  }
  
  // If we ended while still in a table
  if (inTable) {
    tableRanges.push([tableStartIndex, lines.length - 1]);
  }
  
  // Process each table
  for (const [start, end] of tableRanges) {
    // Ensure there's a header separator
    const hasHeaderSeparator = lines[start + 1]?.trim().match(/^\|[-|:]+\|$/);
    
    if (!hasHeaderSeparator && start + 1 <= end) {
      // Insert a separator line after the header
      const headerCells = lines[start].split('|').length - 2; // -2 for the start/end pipes
      const separatorLine = '|' + Array(headerCells).fill('---').join('|') + '|';
      lines.splice(start + 1, 0, separatorLine);
      // Adjust end index since we inserted a new line
      tableRanges.forEach(range => {
        if (range[0] > start) range[0]++;
        if (range[1] >= start) range[1]++;
      });
    }
  }
  
  return lines.join('\n');
}

export function Mdx({ source }: MdxProps) {
  // Pre-process tables in the markdown source
  const processedSource = processMarkdownTables(source);
  
  // Add IDs to headings for linking
  const sourceWithIds = addIdsToHeadings(processedSource);
  
  return (
    <div className="mdx max-w-full break-words">
      <MDXRemote 
        source={sourceWithIds} 
        components={components}
        options={{
          mdxOptions: {
            remarkPlugins: [remarkGfm],
            format: 'md'
          }
        }}
      />
    </div>
  );
}
